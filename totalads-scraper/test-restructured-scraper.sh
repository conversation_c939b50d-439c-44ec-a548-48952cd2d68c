#!/bin/bash

# Test script for the restructured scraper
# Demonstrates normal vs AI-enhanced scraping on single route

BASE_URL="http://localhost:8000"
TEST_URL="https://example.com"

echo "🚀 Testing Restructured Scraper"
echo "================================"
echo "Single route: POST $BASE_URL/scrape"
echo "Test URL: $TEST_URL"
echo ""

# Test 1: Normal Scraping (Fast)
echo "🔍 Test 1: Normal Scraping (Fast Mode)"
echo "Request: {\"url\":\"$TEST_URL\"}"
start_time=$(date +%s%3N)

curl -s -X POST "$BASE_URL/scrape" \
  -H "Content-Type: application/json" \
  -d "{\"url\":\"$TEST_URL\"}" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n" \
  -o normal-result.json

end_time=$(date +%s%3N)
normal_time=$((end_time - start_time))

echo "✅ Normal Scraping Results:"
echo "   Processing Time: ${normal_time}ms"

if [ -f normal-result.json ]; then
  # Extract key information
  title=$(cat normal-result.json | jq -r '.payload.data.title // "N/A"')
  ai_enhanced=$(cat normal-result.json | jq -r '.payload.meta.aiEnhanced // false')
  processing_time=$(cat normal-result.json | jq -r '.payload.meta.processingTime // 0')
  
  echo "   Title: $title"
  echo "   AI Enhanced: $ai_enhanced"
  echo "   Server Processing Time: ${processing_time}ms"
  
  # Check for contact details
  email_count=$(cat normal-result.json | jq '.payload.data.contactDetails.email | length')
  phone_count=$(cat normal-result.json | jq '.payload.data.contactDetails.phone | length')
  echo "   Contact Details: $email_count emails, $phone_count phones"
fi

echo ""
echo "=" $(printf '%.0s=' {1..50})
echo ""

# Test 2: AI-Enhanced Scraping
echo "🤖 Test 2: AI-Enhanced Scraping"
echo "Request: {\"url\":\"$TEST_URL\",\"enableAI\":true}"
start_time=$(date +%s%3N)

curl -s -X POST "$BASE_URL/scrape" \
  -H "Content-Type: application/json" \
  -d "{
    \"url\":\"$TEST_URL\",
    \"enableAI\":true,
    \"aiOptions\":{
      \"maxCost\":0.10,
      \"useCache\":true,
      \"cacheExpiryMinutes\":60
    }
  }" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\n" \
  -o ai-result.json

end_time=$(date +%s%3N)
ai_time=$((end_time - start_time))

echo "✅ AI-Enhanced Scraping Results:"
echo "   Total Processing Time: ${ai_time}ms"

if [ -f ai-result.json ]; then
  # Extract key information
  title=$(cat ai-result.json | jq -r '.payload.data.title // "N/A"')
  ai_enhanced=$(cat ai-result.json | jq -r '.payload.meta.aiEnhanced // false')
  processing_time=$(cat ai-result.json | jq -r '.payload.meta.processingTime // 0')
  ai_processing_time=$(cat ai-result.json | jq -r '.payload.meta.aiProcessingTime // 0')
  ai_cost=$(cat ai-result.json | jq -r '.payload.meta.aiCost // 0')
  
  echo "   Title: $title"
  echo "   AI Enhanced: $ai_enhanced"
  echo "   Server Processing Time: ${processing_time}ms"
  echo "   AI Processing Time: ${ai_processing_time}ms"
  echo "   AI Cost: \$${ai_cost}"
  
  # Check for AI-enhanced data
  if [ "$ai_enhanced" = "true" ]; then
    echo ""
    echo "   🧠 AI-Enhanced Data:"
    
    # Business Intelligence
    industry=$(cat ai-result.json | jq -r '.payload.data.businessIntelligence.industry[0] // "Unknown"')
    confidence=$(cat ai-result.json | jq -r '.payload.data.businessIntelligence.confidence // 0')
    echo "   - Industry: $industry (confidence: ${confidence}%)"
    
    # Entities
    people_count=$(cat ai-result.json | jq '.payload.data.extractedEntities.people | length')
    orgs_count=$(cat ai-result.json | jq '.payload.data.extractedEntities.organizations | length')
    echo "   - Entities: $people_count people, $orgs_count organizations"
    
    # Sentiment
    sentiment=$(cat ai-result.json | jq -r '.payload.data.sentiment.overall // "unknown"')
    sentiment_confidence=$(cat ai-result.json | jq -r '.payload.data.sentiment.confidence // 0')
    echo "   - Sentiment: $sentiment (${sentiment_confidence}% confidence)"
    
    # Check if it's mock data
    mock_data=$(cat ai-result.json | jq -r '.payload.data.mockData // false')
    if [ "$mock_data" = "true" ]; then
      echo "   ⚠️  Note: Using mock AI data (totalads-shared not available)"
    fi
  fi
fi

echo ""
echo "📊 PERFORMANCE COMPARISON:"
echo "=========================="

if [ "$normal_time" -gt 0 ] && [ "$ai_time" -gt 0 ]; then
  speed_ratio=$(echo "scale=2; $ai_time / $normal_time" | bc 2>/dev/null || echo "N/A")
  time_difference=$((ai_time - normal_time))
  
  echo "Normal Scraping:     ${normal_time}ms"
  echo "AI-Enhanced Scraping: ${ai_time}ms"
  echo "Time Difference:     +${time_difference}ms"
  if [ "$speed_ratio" != "N/A" ]; then
    echo "Speed Ratio:         AI is ${speed_ratio}x slower"
  fi
  
  echo ""
  echo "🎯 ARCHITECTURE BENEFITS:"
  echo "========================="
  echo "✅ Single route handles both normal and AI-enhanced scraping"
  echo "✅ Fast default response (normal mode)"
  echo "✅ Optional AI enhancement with graceful fallback"
  echo "✅ No AI dependencies for core functionality"
  echo "✅ Clean, maintainable codebase"
  
  echo ""
  echo "💡 USAGE RECOMMENDATIONS:"
  echo "========================="
  echo "• Use normal mode for fast, reliable data extraction"
  echo "• Use AI mode when enhanced business intelligence is needed"
  echo "• AI enhancement adds business value without breaking core functionality"
  echo "• Perfect balance of speed and intelligence"
  
else
  echo "❌ Could not compare - one or both tests failed"
fi

echo ""
echo "📁 Detailed responses saved to:"
echo "   - normal-result.json"
echo "   - ai-result.json"
echo ""
echo "🔍 To analyze responses in detail:"
echo "   cat normal-result.json | jq ."
echo "   cat ai-result.json | jq ."
