#!/bin/bash

# Simple script to test normal vs AI-enhanced scraper endpoints
# This will demonstrate the performance difference

BASE_URL="http://localhost:8000"
TEST_URL="https://example.com"

echo "🚀 Testing Scraper Performance Comparison"
echo "=========================================="
echo "Test URL: $TEST_URL"
echo ""

# Test 1: Normal Scraper
echo "🔍 Testing Normal Scraper..."
echo "Endpoint: POST $BASE_URL/scrape"
start_time=$(date +%s%3N)

curl -s -X POST "$BASE_URL/scrape" \
  -H "Content-Type: application/json" \
  -d "{\"url\":\"$TEST_URL\"}" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\nSize: %{size_download} bytes\n" \
  -o normal-response.json

end_time=$(date +%s%3N)
normal_time=$((end_time - start_time))

echo "✅ Normal Scraper Results:"
echo "   Processing Time: ${normal_time}ms"
echo "   Response saved to: normal-response.json"

# Check if response was successful
if [ -f normal-response.json ]; then
  success=$(cat normal-response.json | grep -o '"success":[^,]*' | cut -d':' -f2)
  if [ "$success" = "true" ]; then
    title=$(cat normal-response.json | grep -o '"title":"[^"]*"' | cut -d'"' -f4)
    echo "   Title: $title"
    
    # Count contact details
    contact_count=$(cat normal-response.json | grep -o '"contactDetails":{[^}]*}' | wc -c)
    echo "   Contact Data Size: ${contact_count} characters"
  else
    echo "   ❌ Normal scraper failed"
    cat normal-response.json
  fi
fi

echo ""
echo "=" $(printf '%.0s=' {1..50})
echo ""

# Test 2: AI-Enhanced Scraper
echo "🤖 Testing AI-Enhanced Scraper..."
echo "Endpoint: POST $BASE_URL/api/ai-scraper/scrape"
start_time=$(date +%s%3N)

curl -s -X POST "$BASE_URL/api/ai-scraper/scrape" \
  -H "Content-Type: application/json" \
  -d "{
    \"url\":\"$TEST_URL\",
    \"options\":{
      \"enableAI\":true,
      \"aiOptions\":{
        \"maxCost\":0.10,
        \"useCache\":true,
        \"cacheExpiryMinutes\":60
      }
    }
  }" \
  -w "\nHTTP Status: %{http_code}\nTime: %{time_total}s\nSize: %{size_download} bytes\n" \
  -o ai-response.json

end_time=$(date +%s%3N)
ai_time=$((end_time - start_time))

echo "✅ AI-Enhanced Scraper Results:"
echo "   Total Processing Time: ${ai_time}ms"
echo "   Response saved to: ai-response.json"

# Check if response was successful
if [ -f ai-response.json ]; then
  success=$(cat ai-response.json | grep -o '"success":[^,]*' | cut -d':' -f2)
  if [ "$success" = "true" ]; then
    title=$(cat ai-response.json | grep -o '"title":"[^"]*"' | cut -d'"' -f4)
    echo "   Title: $title"
    
    # Extract AI-specific metrics
    ai_processing_time=$(cat ai-response.json | grep -o '"aiProcessingTime":[^,]*' | cut -d':' -f2)
    ai_cost=$(cat ai-response.json | grep -o '"aiCost":[^,]*' | cut -d':' -f2)
    confidence=$(cat ai-response.json | grep -o '"confidence":[^,]*' | cut -d':' -f2)
    
    echo "   AI Processing Time: ${ai_processing_time}ms"
    echo "   AI Cost: \$${ai_cost}"
    echo "   Business Intelligence Confidence: ${confidence}%"
  else
    echo "   ❌ AI-enhanced scraper failed"
    cat ai-response.json
  fi
fi

echo ""
echo "📊 PERFORMANCE COMPARISON:"
echo "=========================="

if [ "$normal_time" -gt 0 ] && [ "$ai_time" -gt 0 ]; then
  speed_ratio=$(echo "scale=2; $ai_time / $normal_time" | bc)
  time_difference=$((ai_time - normal_time))
  
  echo "Normal Scraper:     ${normal_time}ms"
  echo "AI-Enhanced Scraper: ${ai_time}ms"
  echo "Time Difference:    +${time_difference}ms"
  echo "Speed Ratio:        AI is ${speed_ratio}x slower"
  
  echo ""
  echo "🎯 CONCLUSION:"
  echo "=============="
  
  if [ "$ai_time" -gt $((normal_time * 3)) ]; then
    echo "❌ AI enhancement is significantly slower (>3x)"
    echo "💡 Recommendation: Use normal scraper for most cases"
  elif [ "$ai_time" -gt $((normal_time * 2)) ]; then
    echo "⚠️  AI enhancement is moderately slower (2-3x)"
    echo "💡 Recommendation: Use AI selectively for high-value cases"
  else
    echo "✅ AI enhancement has acceptable performance impact (<2x)"
    echo "💡 Recommendation: AI enhancement may be worth the cost"
  fi
else
  echo "❌ Could not compare - one or both tests failed"
fi

echo ""
echo "📁 Detailed responses saved to:"
echo "   - normal-response.json"
echo "   - ai-response.json"
echo ""
echo "🔍 To analyze responses in detail:"
echo "   cat normal-response.json | jq ."
echo "   cat ai-response.json | jq ."
