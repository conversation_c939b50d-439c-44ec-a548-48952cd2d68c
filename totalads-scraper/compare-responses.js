#!/usr/bin/env node

/**
 * <PERSON>ript to compare normal scraper response vs AI-enhanced response
 * This will help analyze the performance difference and value of AI processing
 */

import axios from "axios";
import fs from "fs";

const BASE_URL = "http://localhost:8000";
const TEST_URL = "https://example.com"; // Using a simpler site for testing

async function testNormalScraper() {
	console.log("🔍 Testing Normal Scraper...");
	const startTime = Date.now();

	try {
		const response = await axios.post(
			`${BASE_URL}/scrape`,
			{
				url: TEST_URL,
			},
			{
				timeout: 180000, // 3 minutes timeout
			},
		);

		const endTime = Date.now();
		const processingTime = endTime - startTime;

		console.log("✅ Normal Scraper Response:");
		console.log(
			`   Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`,
		);
		console.log(`   Title: ${response.data.data.title}`);
		console.log(`   Description: ${response.data.data.desc}`);
		console.log(
			`   Links Found: ${response.data.data.nestedLinks?.length || 0}`,
		);
		console.log(
			`   Text Length: ${response.data.data.text?.length || 0} characters`,
		);
		console.log(
			`   Contact Details: ${JSON.stringify(response.data.data.contactDetails, null, 2)}`,
		);
		console.log(
			`   About Data: ${JSON.stringify(response.data.data.aboutData, null, 2)}`,
		);

		// Save response to file for detailed analysis
		fs.writeFileSync(
			"normal-response.json",
			JSON.stringify(response.data, null, 2),
		);

		return {
			success: true,
			processingTime,
			data: response.data.data,
		};
	} catch (error) {
		const endTime = Date.now();
		const processingTime = endTime - startTime;

		console.log("❌ Normal Scraper Failed:");
		console.log(
			`   Processing Time: ${processingTime}ms (${(processingTime / 1000).toFixed(2)}s)`,
		);
		console.log(`   Error: ${error.message}`);

		return {
			success: false,
			processingTime,
			error: error.message,
		};
	}
}

async function testAIScraper() {
	console.log("🤖 Testing AI-Enhanced Scraper...");
	const startTime = Date.now();

	try {
		const response = await axios.post(
			`${BASE_URL}/api/ai-scraper/scrape`,
			{
				url: TEST_URL,
				options: {
					enableAI: true,
					aiOptions: {
						maxCost: 0.5, // Increase budget for comprehensive analysis
						useCache: true,
						cacheExpiryMinutes: 60,
					},
				},
			},
			{
				timeout: 300000, // 5 minutes timeout for AI processing
			},
		);

		const endTime = Date.now();
		const totalProcessingTime = endTime - startTime;

		console.log("✅ AI-Enhanced Scraper Response:");
		console.log(
			`   Total Processing Time: ${totalProcessingTime}ms (${(totalProcessingTime / 1000).toFixed(2)}s)`,
		);
		console.log(
			`   Basic Processing Time: ${response.data.data.processingTime}ms`,
		);
		console.log(
			`   AI Processing Time: ${response.data.data.aiProcessingTime}ms (${(response.data.data.aiProcessingTime / 1000).toFixed(2)}s)`,
		);
		console.log(
			`   AI Cost: $${response.data.data.aiCost?.toFixed(4) || "0.0000"}`,
		);
		console.log(`   Title: ${response.data.data.title}`);
		console.log(`   Description: ${response.data.data.description}`);
		console.log(`   Links Found: ${response.data.data.links?.length || 0}`);
		console.log(
			`   Content Length: ${response.data.data.content?.length || 0} characters`,
		);

		// AI-Enhanced Data
		console.log("\n🧠 AI-Enhanced Intelligence:");
		console.log(
			`   Business Intelligence Confidence: ${response.data.data.businessIntelligence?.confidence || 0}%`,
		);
		console.log(
			`   Industry: ${response.data.data.businessIntelligence?.industry?.join(", ") || "N/A"}`,
		);
		console.log(
			`   Market Position: ${response.data.data.businessIntelligence?.marketPosition || "N/A"}`,
		);
		console.log(
			`   Company Size: ${response.data.data.businessIntelligence?.companySize || "N/A"}`,
		);
		console.log(
			`   Revenue Range: ${response.data.data.businessIntelligence?.revenueRange || "N/A"}`,
		);
		console.log(
			`   Sentiment: ${response.data.data.sentiment?.overall || "N/A"} (${response.data.data.sentiment?.confidence || 0}% confidence)`,
		);

		// Enhanced Contact Info
		console.log("\n📞 Enhanced Contact Information:");
		console.log(
			`   Enhanced Contact: ${JSON.stringify(response.data.data.enhancedContactInfo, null, 2)}`,
		);

		// Extracted Entities
		console.log("\n🏷️ Extracted Entities:");
		console.log(
			`   People: ${response.data.data.extractedEntities?.people?.join(", ") || "None"}`,
		);
		console.log(
			`   Organizations: ${response.data.data.extractedEntities?.organizations?.join(", ") || "None"}`,
		);
		console.log(
			`   Locations: ${response.data.data.extractedEntities?.locations?.join(", ") || "None"}`,
		);
		console.log(
			`   Products: ${response.data.data.extractedEntities?.products?.join(", ") || "None"}`,
		);

		// Recommendations
		console.log("\n💡 AI Recommendations:");
		response.data.data.recommendations?.forEach((rec, index) => {
			console.log(`   ${index + 1}. ${rec}`);
		});

		// Save response to file for detailed analysis
		fs.writeFileSync(
			"ai-response.json",
			JSON.stringify(response.data, null, 2),
		);

		return {
			success: true,
			totalProcessingTime,
			basicProcessingTime: response.data.data.processingTime,
			aiProcessingTime: response.data.data.aiProcessingTime,
			aiCost: response.data.data.aiCost,
			data: response.data.data,
		};
	} catch (error) {
		const endTime = Date.now();
		const totalProcessingTime = endTime - startTime;

		console.log("❌ AI-Enhanced Scraper Failed:");
		console.log(
			`   Total Processing Time: ${totalProcessingTime}ms (${(totalProcessingTime / 1000).toFixed(2)}s)`,
		);
		console.log(`   Error: ${error.message}`);

		return {
			success: false,
			totalProcessingTime,
			error: error.message,
		};
	}
}

async function compareResponses() {
	console.log(`🚀 Starting Response Comparison for: ${TEST_URL}\n`);

	// Test normal scraper
	const normalResult = await testNormalScraper();
	console.log("\n" + "=".repeat(80) + "\n");

	// Test AI-enhanced scraper
	const aiResult = await testAIScraper();
	console.log("\n" + "=".repeat(80) + "\n");

	// Comparison Summary
	console.log("📊 COMPARISON SUMMARY:");
	console.log("=".repeat(50));

	if (normalResult.success && aiResult.success) {
		const speedDifference =
			aiResult.totalProcessingTime - normalResult.processingTime;
		const speedRatio = (
			aiResult.totalProcessingTime / normalResult.processingTime
		).toFixed(2);

		console.log(
			`Normal Scraper Time: ${normalResult.processingTime}ms (${(normalResult.processingTime / 1000).toFixed(2)}s)`,
		);
		console.log(
			`AI Scraper Time: ${aiResult.totalProcessingTime}ms (${(aiResult.totalProcessingTime / 1000).toFixed(2)}s)`,
		);
		console.log(
			`AI Processing Only: ${aiResult.aiProcessingTime}ms (${(aiResult.aiProcessingTime / 1000).toFixed(2)}s)`,
		);
		console.log(
			`Speed Difference: +${speedDifference}ms (+${(speedDifference / 1000).toFixed(2)}s)`,
		);
		console.log(`AI is ${speedRatio}x slower than normal scraping`);
		console.log(`AI Cost: $${aiResult.aiCost?.toFixed(4) || "0.0000"}`);

		console.log("\n🎯 VALUE ANALYSIS:");
		console.log("Normal scraper provides:");
		console.log("  ✓ Basic title, description, links");
		console.log("  ✓ Raw text content");
		console.log("  ✓ Basic contact extraction");
		console.log("  ✓ Fast processing");

		console.log("\nAI-enhanced scraper adds:");
		console.log("  ✓ Business intelligence analysis");
		console.log("  ✓ Enhanced contact information");
		console.log(
			"  ✓ Entity extraction (people, orgs, locations, products)",
		);
		console.log("  ✓ Sentiment analysis");
		console.log("  ✓ Industry classification");
		console.log("  ✓ Market position analysis");
		console.log("  ✓ Optimization recommendations");
		console.log("  ❌ Significantly slower processing");
		console.log("  ❌ Additional cost per request");
	} else {
		console.log("❌ Cannot compare - one or both scrapers failed");
		if (!normalResult.success) {
			console.log(`Normal scraper error: ${normalResult.error}`);
		}
		if (!aiResult.success) {
			console.log(`AI scraper error: ${aiResult.error}`);
		}
	}

	console.log("\n📁 Detailed responses saved to:");
	console.log("  - normal-response.json");
	console.log("  - ai-response.json");
}

// Run the comparison
compareResponses().catch(console.error);
