/**
 * AI-Enhanced Scraper Service
 * Integrates AI functionality for intelligent business data extraction
 */

import { Page } from 'puppeteer';
import {
    aiCostMonitor, AIProcessingResult, aiService, BusinessIntelligence, EnhancedContactInfo
} from 'totalads-shared';

import { extractEnhancedAboutData } from '../extractors/enhanced-scraper';
import { BrowserTask, EnhancedScrapeResult, ScrapeResult } from '../models/scraper.model';
import { DataProcessorService } from '../processors/data-processor.service';
import { handleError } from '../utils/error-handler';
import logger from '../utils/logger';
import { BrowserController } from '../utils/scraper/browser-controller';
import { ContentExtractor } from '../utils/scraper/content-extractor';
import { TableHandler } from '../utils/scraper/table-handler';

export interface AIEnhancedScrapeResult extends EnhancedScrapeResult {
	businessIntelligence: BusinessIntelligence;
	enhancedContactInfo: EnhancedContactInfo;
	extractedEntities: {
		people: string[];
		organizations: string[];
		locations: string[];
		products: string[];
	};
	sentiment: {
		overall: "positive" | "neutral" | "negative";
		confidence: number;
	};
	aiProcessingTime: number;
	aiCost: number;
	recommendations: string[];
}

/**
 * AI-Enhanced Scraper Service with business intelligence extraction
 */
export class AIEnhancedScraperService {
	private browserController: BrowserController;
	private contentExtractor: ContentExtractor;
	private tableHandler: TableHandler;
	private dataProcessorService: DataProcessorService;

	constructor() {
		this.browserController = new BrowserController();
		this.contentExtractor = new ContentExtractor();
		this.tableHandler = new TableHandler();
		this.dataProcessorService = new DataProcessorService();

		logger.info("AI-Enhanced Scraper Service initialized");

		// Set up cost monitoring alerts
		aiCostMonitor.onAlert((alert) => {
			logger.warn(`AI Cost Alert: ${alert.message}`, {
				type: alert.type,
				severity: alert.severity,
				currentValue: alert.currentValue,
				threshold: alert.threshold,
			});
		});
	}

	/**
	 * Scrape and analyze website with AI enhancement
	 */
	async scrapeWithAI(task: BrowserTask): Promise<AIEnhancedScrapeResult> {
		const startTime = Date.now();
		const url = task.url;

		try {
			logger.info(`Starting AI-enhanced scrape for: ${url}`);

			// Check if we can make AI requests within budget
			const budgetCheck = await aiCostMonitor.canMakeRequest();
			if (!budgetCheck.allowed) {
				logger.warn(
					`AI processing skipped due to budget limits: ${budgetCheck.reason}`,
				);
				return this.getFallbackResult(url, budgetCheck.reason);
			}

			// Execute basic scraping first
			const basicResult = await this.executeBasicScraping(task);

			// Extract company name from title or URL for AI context
			const companyName = this.extractCompanyName(basicResult.title, url);

			// Scrape important pages for comprehensive analysis
			const additionalContent = await this.scrapeImportantPages(
				basicResult.nestedLinks,
				url,
			);

			// Combine main page content with additional pages
			const combinedContent = this.combineContentForAnalysis(
				basicResult.text,
				additionalContent,
			);

			// Prepare content for AI processing (optimize for cost)
			const optimizedContent = this.prepareContentForAI(combinedContent);

			// Check content length and warn if it might be expensive
			const estimatedCost = this.estimateAICost(optimizedContent);
			logger.info(
				`Estimated AI processing cost: $${estimatedCost.toFixed(4)}`,
			);

			// Final budget check with estimated cost
			const finalBudgetCheck =
				await aiCostMonitor.canMakeRequest(estimatedCost);
			if (!finalBudgetCheck.allowed) {
				logger.warn(
					`AI processing skipped due to estimated cost: ${finalBudgetCheck.reason}`,
				);
				return this.getFallbackResult(
					url,
					finalBudgetCheck.reason || "Budget check failed",
					basicResult,
				);
			}

			// Enhance content with extracted links for better AI analysis
			const enhancedContent = this.enhanceContentWithLinks(
				optimizedContent,
				basicResult.nestedLinks,
			);

			// Process with AI
			const aiStartTime = Date.now();
			const aiResult = await aiService.processBusinessAnalysis(
				enhancedContent,
				basicResult.contactDetails,
				companyName,
				url,
			);
			const aiProcessingTime = Date.now() - aiStartTime;

			// Record AI usage for cost tracking
			aiCostMonitor.recordUsage(
				aiResult.usage.estimatedCost,
				aiResult.usage.totalTokens,
			);

			// Get optimization recommendations
			const recommendations =
				aiCostMonitor.getOptimizationRecommendations();

			// Combine results
			const enhancedResult: AIEnhancedScrapeResult = {
				...basicResult,
				url,
				timestamp: new Date(),
				processingTime: Date.now() - startTime,
				status: "success",
				businessIntelligence: aiResult.businessIntelligence,
				enhancedContactInfo: aiResult.enhancedContactInfo,
				extractedEntities: aiResult.extractedEntities,
				sentiment: aiResult.sentiment,
				aiProcessingTime,
				aiCost: aiResult.usage.estimatedCost,
				recommendations,
			};

			logger.info(`AI-enhanced scrape completed for ${url}`, {
				totalTime: enhancedResult.processingTime,
				aiTime: aiProcessingTime,
				aiCost: aiResult.usage.estimatedCost,
				confidence: aiResult.businessIntelligence.confidence,
			});

			return enhancedResult;
		} catch (error) {
			const errorMessage = `Failed to perform AI-enhanced scrape for ${url}`;
			handleError(
				error as Error,
				"AIEnhancedScraperService.scrapeWithAI",
			);

			return {
				...this.getDefaultScrapeResult(),
				url,
				timestamp: new Date(),
				processingTime: Date.now() - startTime,
				status: "failed",
				error: (error as Error).message,
				businessIntelligence: this.getDefaultBusinessIntelligence(),
				enhancedContactInfo: this.getDefaultContactInfo(),
				extractedEntities: {
					people: [],
					organizations: [],
					locations: [],
					products: [],
				},
				sentiment: { overall: "neutral", confidence: 0 },
				aiProcessingTime: 0,
				aiCost: 0,
				recommendations: [],
			};
		}
	}

	/**
	 * Execute basic scraping without AI
	 */
	private async executeBasicScraping(
		task: BrowserTask,
	): Promise<ScrapeResult> {
		return await this.browserController.executeWithPage<ScrapeResult>(
			task.url,
			async (page) => {
				// Extract basic metadata
				const metadata =
					await this.contentExtractor.getTitleAndDesc(page);

				// Extract links
				const links = await this.contentExtractor.extractLinks(page);

				// Extract and convert HTML to text
				const htmlText =
					await this.contentExtractor.convertHTMLToText(page);

				// Extract tables
				const tables =
					await this.tableHandler.extractTablesAndReplacePlaceholders(
						page,
					);

				// Process text with tables
				const finalText =
					tables && tables.length > 0
						? this.tableHandler.replaceTablePlaceholders(
								tables,
								htmlText,
							)
						: htmlText;

				// Extract contact details
				const contactDetails =
					this.dataProcessorService.extractContactDetails(finalText);

				// Extract enhanced about data
				const aboutData = await extractEnhancedAboutData(
					page,
					task.url,
				);

				return {
					title: metadata.title,
					desc: metadata.desc,
					nestedLinks: links,
					text: finalText,
					contactDetails,
					aboutData,
				};
			},
		);
	}

	/**
	 * Extract company name from title or URL
	 */
	private extractCompanyName(title: string | null, url: string): string {
		if (title) {
			// Remove common suffixes and clean up
			const cleaned = title
				.replace(/\s*[-|–]\s*.*/g, "") // Remove everything after dash
				.replace(/\s*\|\s*.*/g, "") // Remove everything after pipe
				.replace(/\s*(Home|Homepage|Welcome).*$/i, "") // Remove common page indicators
				.trim();

			if (cleaned.length > 0 && cleaned.length < 100) {
				return cleaned;
			}
		}

		// Fallback to domain name
		try {
			const domain = new URL(url).hostname.replace("www.", "");
			return domain.split(".")[0] || "Unknown Company";
		} catch {
			return "Unknown Company";
		}
	}

	/**
	 * Scrape important pages for comprehensive business analysis
	 */
	private async scrapeImportantPages(
		links: string[],
		baseUrl: string,
	): Promise<{ [key: string]: string }> {
		const importantPages: { [key: string]: string } = {};

		// Define important page patterns
		const importantPatterns = [
			{ pattern: /\/about/i, key: "about", priority: 1 },
			{ pattern: /\/contact/i, key: "contact", priority: 1 },
			{ pattern: /\/team/i, key: "team", priority: 2 },
			{ pattern: /\/company/i, key: "company", priority: 1 },
			{ pattern: /\/careers/i, key: "careers", priority: 3 },
			{ pattern: /\/locations/i, key: "locations", priority: 2 },
			{ pattern: /\/services/i, key: "services", priority: 2 },
			{ pattern: /\/products/i, key: "products", priority: 2 },
			{ pattern: /\/leadership/i, key: "leadership", priority: 2 },
			{ pattern: /\/history/i, key: "history", priority: 3 },
			{ pattern: /\/mission/i, key: "mission", priority: 2 },
			{ pattern: /\/values/i, key: "values", priority: 3 },
		];

		// Filter and prioritize important links
		const importantLinks = links
			.map((link) => {
				const match = importantPatterns.find((p) =>
					p.pattern.test(link),
				);
				return match
					? { url: link, key: match.key, priority: match.priority }
					: null;
			})
			.filter(Boolean)
			.sort((a, b) => a!.priority - b!.priority)
			.slice(0, 5); // Limit to top 5 most important pages

		logger.info(
			`Found ${importantLinks.length} important pages to analyze`,
		);

		// Scrape each important page
		for (const linkInfo of importantLinks) {
			if (!linkInfo) continue;

			try {
				// Convert relative URLs to absolute
				const absoluteUrl = linkInfo.url.startsWith("http")
					? linkInfo.url
					: new URL(linkInfo.url, baseUrl).toString();

				logger.info(
					`Scraping important page: ${linkInfo.key} - ${absoluteUrl}`,
				);

				const pageContent =
					await this.browserController.executeWithPage<string>(
						absoluteUrl,
						async (page) => {
							// Extract text content from the page
							const content = await page.evaluate(() => {
								// Remove script and style elements
								const scripts = document.querySelectorAll(
									"script, style, nav, header, footer",
								);
								scripts.forEach((el) => el.remove());

								// Get main content areas
								const mainContent =
									document.querySelector("main") ||
									document.querySelector('[role="main"]') ||
									document.querySelector(".content") ||
									document.querySelector("#content") ||
									document.body;

								return mainContent?.innerText || "";
							});

							return content;
						},
					);

				if (pageContent && pageContent.trim().length > 100) {
					importantPages[linkInfo.key] = pageContent.trim();
					logger.info(
						`Successfully scraped ${linkInfo.key} page: ${pageContent.length} characters`,
					);
				}

				// Small delay to be respectful
				await new Promise((resolve) => setTimeout(resolve, 500));
			} catch (error) {
				logger.warn(
					`Failed to scrape ${linkInfo.key} page: ${(error as Error).message}`,
				);
			}
		}

		return importantPages;
	}

	/**
	 * Combine main page content with additional pages for comprehensive analysis
	 */
	private combineContentForAnalysis(
		mainContent: string,
		additionalContent: { [key: string]: string },
	): string {
		let combinedContent = `MAIN PAGE CONTENT:\n${mainContent}\n\n`;

		// Add content from important pages with clear labels
		for (const [pageType, content] of Object.entries(additionalContent)) {
			if (content && content.trim().length > 50) {
				combinedContent += `${pageType.toUpperCase()} PAGE CONTENT:\n${content.substring(0, 2000)}\n\n`;
			}
		}

		logger.info(
			`Combined content length: ${combinedContent.length} characters from ${Object.keys(additionalContent).length + 1} pages`,
		);

		return combinedContent;
	}

	/**
	 * Enhance content with extracted links for better AI analysis
	 */
	private enhanceContentWithLinks(content: string, links: string[]): string {
		// Filter and categorize social media links
		const socialLinks = links.filter(
			(link) =>
				link.includes("facebook.com") ||
				link.includes("twitter.com") ||
				link.includes("x.com") ||
				link.includes("linkedin.com") ||
				link.includes("instagram.com") ||
				link.includes("youtube.com") ||
				link.includes("pinterest.com") ||
				link.includes("tiktok.com"),
		);

		// Filter contact and about page links
		const importantLinks = links.filter(
			(link) =>
				link.includes("/contact") ||
				link.includes("/about") ||
				link.includes("/team") ||
				link.includes("/company") ||
				link.includes("/careers") ||
				link.includes("/locations"),
		);

		// Add social media information to content
		let enhancedContent = content;
		if (socialLinks.length > 0) {
			enhancedContent +=
				"\n\nSocial Media Presence: " + socialLinks.join(", ");
		}

		// Add important page links
		if (importantLinks.length > 0) {
			enhancedContent +=
				"\n\nImportant Pages: " + importantLinks.join(", ");
		}

		return enhancedContent;
	}

	/**
	 * Prepare content for AI processing with cost optimization
	 */
	private prepareContentForAI(content: string): string {
		// Remove excessive whitespace
		let optimized = content.replace(/\s+/g, " ").trim();

		// Limit content length to control costs (increased for multi-page analysis)
		const maxLength = 6000;
		if (optimized.length > maxLength) {
			// Try to cut at sentence boundaries
			const sentences = optimized.split(/[.!?]+/);
			let result = "";

			for (const sentence of sentences) {
				if (result.length + sentence.length > maxLength) {
					break;
				}
				result += sentence + ". ";
			}

			optimized = result.trim();
		}

		return optimized;
	}

	/**
	 * Estimate AI processing cost
	 */
	private estimateAICost(content: string): number {
		// Rough estimation: ~4 chars per token, multiple AI calls
		const estimatedTokens = Math.ceil(content.length / 4) * 3; // 3 AI calls
		return estimatedTokens * 0.000075; // Gemini Flash pricing
	}

	/**
	 * Get fallback result when AI processing is skipped
	 */
	private getFallbackResult(
		url: string,
		reason: string,
		basicResult?: ScrapeResult,
	): AIEnhancedScrapeResult {
		const base = basicResult || this.getDefaultScrapeResult();

		return {
			...base,
			url,
			timestamp: new Date(),
			processingTime: 0,
			status: "partial",
			error: `AI processing skipped: ${reason}`,
			businessIntelligence: this.getDefaultBusinessIntelligence(),
			enhancedContactInfo: this.getDefaultContactInfo(),
			extractedEntities: {
				people: [],
				organizations: [],
				locations: [],
				products: [],
			},
			sentiment: { overall: "neutral", confidence: 0 },
			aiProcessingTime: 0,
			aiCost: 0,
			recommendations: [
				"AI processing was skipped due to budget constraints",
			],
		};
	}

	/**
	 * Get default scrape result
	 */
	private getDefaultScrapeResult(): ScrapeResult {
		return {
			title: null,
			desc: null,
			nestedLinks: [],
			text: "",
			contactDetails: {},
			aboutData: {},
		};
	}

	/**
	 * Get default business intelligence
	 */
	private getDefaultBusinessIntelligence(): BusinessIntelligence {
		return {
			companyType: "unknown",
			industry: [],
			businessModel: "unknown",
			targetMarket: [],
			keyServices: [],
			competitiveAdvantages: [],
			marketPosition: "unknown",
			technologies: [],
			partnerships: [],
			certifications: [],
			awards: [],
			socialPresence: { platforms: [], engagement: "unknown" },
			riskFactors: [],
			opportunities: [],
			confidence: 0,
		};
	}

	/**
	 * Get default contact info
	 */
	private getDefaultContactInfo(): EnhancedContactInfo {
		return {
			emails: [],
			phones: [],
			addresses: [],
			socialMedia: [],
			website: { primary: "", additional: [] },
		};
	}

	/**
	 * Get cost statistics
	 */
	getCostStats() {
		return aiCostMonitor.getUsageStats();
	}

	/**
	 * Set AI budget
	 */
	setAIBudget(budget: {
		daily?: number;
		monthly?: number;
		perRequest?: number;
	}) {
		aiCostMonitor.setBudget(budget);
	}
}

// Export singleton instance
export const aiEnhancedScraperService = new AIEnhancedScraperService();
export default aiEnhancedScraperService;
