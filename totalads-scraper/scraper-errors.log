{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:26:10.053Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:28:51.877Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:39:41.670Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:13.085Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T19:40:47.929Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:05:55.865Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.869Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:05:55.871Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:06:44.804Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.810Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:06:44.812Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:09:01.443Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.449Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:09:01.452Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-26T20:10:28.628Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.630Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:652:1656), <anonymous>:0:2040)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:822:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-26T20:10:28.632Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:19.996Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:19.999Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:20.007Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:20:40.669Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.671Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:20:40.672Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","stack":"ScraperError: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:22:7)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:137:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:79:22)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Navigation timeout of 30000 ms exceeded","timestamp":"2025-06-28T08:21:49.711Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:05.124Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.127Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:05.128Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:22:32.303Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.308Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:133:24)","timestamp":"2025-06-28T08:22:32.311Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:31:38.636Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.642Z"}
{"level":"error","message":"Error finding about and team pages: logger is not defined","name":"ReferenceError","stack":"ReferenceError: logger is not defined\n    at evaluate (evaluate at findAboutAndTeamPages (file:///Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:661:1656), <anonymous>:0:1937)\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:304:13)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async findAboutAndTeamPages (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:831:28)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:112:30)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:31:38.645Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:34:28.313Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:34:28.324Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:34:29.882Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:39:12.752Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:39:12.758Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:40:10.722Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:40:10.724Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:14.792Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:40:18.678Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T08:43:34.681Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T08:43:34.686Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T08:43:36.042Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:01:32.426Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:01:32.431Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:56:20)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1117:25)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:01:33.842Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T13:33:19.420Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T13:33:19.425Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T13:33:21.248Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:08:36.699Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:08:36.705Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:08:37.820Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:16:10.357Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:16:10.361Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:16:11.519Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:24:48.720Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:24:48.723Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:24:50.028Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:31:55.001Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:31:55.005Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:31:56.264Z"}
{"level":"error","message":"Error extracting tables: ReferenceError: __name is not defined","timestamp":"2025-06-28T14:33:01.777Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:33:01.779Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:33:03.161Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:36:33.581Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:37:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:36:35.187Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:38:34.425Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1188:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:38:36.021Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:42:30.233Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:42:31.486Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:47:05.815Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:47:06.924Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:108:35)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:151:26)","timestamp":"2025-06-28T14:57:25.956Z"}
{"level":"error","message":"Error extracting about info: Invalid regular expression: missing /","name":"SyntaxError","stack":"SyntaxError: Invalid regular expression: missing /\n    at ExecutionContext.#evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:266:15)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ExecutionContext.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/ExecutionContext.ts:157:12)\n    at async IsolatedWorld.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/cdp/IsolatedWorld.ts:143:12)\n    at async CdpFrame.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/src/api/Frame.ts:478:12)\n    at async CdpPage.evaluate (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/puppeteer-core@21.11.0/node_modules/puppeteer-core/lib/esm/puppeteer/api/Page.js:814:20)\n    at async extractAboutInfo (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:36:18)\n    at async extractCompanyInfoFromSite (/Users/<USER>/Desktop/totalads/totalads-scraper/src/extractors/about-info.ts:1189:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:120:33)","timestamp":"2025-06-28T14:57:27.186Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:21:20.595Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:21:20.596Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: net::ERR_ABORTED at hhttps://www.semrush.com/","timestamp":"2025-06-28T16:21:20.596Z"}
{"level":"error","message":"Error extracting from https://www.semrush.com/company/: Navigating frame was detached","timestamp":"2025-06-28T16:21:36.929Z"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: Timeout hit: 30000","stack":"ScraperError: Error in BrowserController.executeWithPage: Timeout hit: 30000\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:22:17.436Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.executeWithPage: Timeout hit: 30000","stack":"ScraperError: Error in BrowserController.executeWithPage: Timeout hit: 30000\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:25:6)\n    at BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:168:7)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:27:22)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_hgkuyh6lxfp7ryn24pt53glqpu/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","timestamp":"2025-06-28T16:22:17.436Z"}
{"level":"error","message":"Error in scrape controller: Error in BrowserController.executeWithPage: Timeout hit: 30000","timestamp":"2025-06-28T16:22:17.436Z"}
{"level":"error","message":"Error extracting from https://www.semrush.com/company/stories/: Navigating frame was detached","timestamp":"2025-06-28T16:22:17.527Z"}
{"code":"UNKNOWN_ERROR","context":"ContentExtractor.getTitleAndDesc","level":"error","message":"Error in ContentExtractor.getTitleAndDesc: Protocol error (Runtime.callFunctionOn): Target closed","retryable":false,"stack":"ScraperError: Error in ContentExtractor.getTitleAndDesc: Protocol error (Runtime.callFunctionOn): Target closed\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at ContentExtractor.getTitleAndDesc (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/content-extractor.ts:67:14)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:178:26)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:208:22)\n    at async <anonymous> (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/puppeteer-cluster/util.ts:85:13)","statusCode":500,"timestamp":"2025-06-28T18:44:59.956Z"}
{"attempt":2,"error":"net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","level":"error","message":"large-website-scrape-https://www.semrush.com/ failed with non-retryable error:","timestamp":"2025-06-28T18:45:22.932Z"}
{"code":"NAVIGATION_FAILED","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:45:22.934Z"}
{"code":"NAVIGATION_FAILED","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:45:22.935Z"}
{"error":"Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_SOCKET_NOT_CONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T18:45:22.935Z","url":"https://www.semrush.com/"}
{"attempt":1,"error":"net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","level":"error","message":"large-website-scrape-https://www.semrush.com/ failed with non-retryable error:","timestamp":"2025-06-28T18:46:33.668Z"}
{"code":"NAVIGATION_FAILED","context":"BrowserController.executeWithPage","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:46:33.670Z"}
{"code":"NAVIGATION_FAILED","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","retryAfter":15,"retryable":true,"stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":502,"timestamp":"2025-06-28T18:46:33.670Z"}
{"error":"Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.executeWithPage: net::ERR_INTERNET_DISCONNECTED at https://www.semrush.com/\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:239:8)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:174:12)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:88:27)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T18:46:33.670Z","url":"https://www.semrush.com/"}
{"error":"[\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 1,\n    \"type\": \"number\",\n    \"inclusive\": true,\n    \"exact\": false,\n    \"message\": \"Number must be less than or equal to 1\",\n    \"path\": [\n      \"options\",\n      \"aiOptions\",\n      \"maxCost\"\n    ]\n  }\n]","level":"error","message":"AI-enhanced scrape failed","stack":"ZodError: [\n  {\n    \"code\": \"too_big\",\n    \"maximum\": 1,\n    \"type\": \"number\",\n    \"inclusive\": true,\n    \"exact\": false,\n    \"message\": \"Number must be less than or equal to 1\",\n    \"path\": [\n      \"options\",\n      \"aiOptions\",\n      \"maxCost\"\n    ]\n  }\n]\n    at get error (file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs:587:31)\n    at ZodObject.parse (file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/zod@3.24.2/node_modules/zod/lib/index.mjs:663:22)\n    at AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:67:49)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at next (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:149:13)\n    at Route.dispatch (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/route.js:119:3)\n    at Layer.handle [as handle_request] (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/layer.js:95:5)\n    at /Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:284:15\n    at Function.process_params (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:346:12)\n    at next (/Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/express@4.21.2/node_modules/express/lib/router/index.js:280:10)","timestamp":"2025-06-28T18:57:00.952Z","url":"https://www.semrush.com/"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:08.090Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z"}
{"code":"UNKNOWN_ERROR","level":"error","message":"Error in scrape controller: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[8792:11130602:0629/005008.082957:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[8792:11130602:0629/005008.083110:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:08.091Z","url":"https://www.semrush.com/"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:38.103Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z"}
{"code":"UNKNOWN_ERROR","context":"ScraperService.scrape","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async ScraperService.scrape (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/scraper.service.ts:87:5)\n    at async ScraperController.scrapeUrl (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/scraper.controller.ts:29:19)\n    at async router.post.expressAsyncHandler.validationSchema (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/routes.ts:20:7)\n    at async file:///Users/<USER>/Desktop/totalads/totalads-scraper/node_modules/.pnpm/totalads-shared@<EMAIL>+TotalAds+tota_zshffmfwcf2zx26zplrcc5hpue/node_modules/totalads-shared/dist/chunk-C2V5F7K2.mjs:24:16","statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z"}
{"code":"UNKNOWN_ERROR","level":"error","message":"Error in scrape controller: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[9038:11131714:0629/005038.099396:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/SingletonLock: File exists (17)\n[9038:11131714:0629/005038.099574:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:20:38.104Z","url":"https://www.semrush.com/"}
{"code":"UNKNOWN_ERROR","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-28T19:28:42.070Z"}
{"code":"UNKNOWN_ERROR","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com/:","retryable":false,"statusCode":500,"timestamp":"2025-06-28T19:28:42.071Z"}
{"code":"UNKNOWN_ERROR","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":500,"timestamp":"2025-06-28T19:28:42.071Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13170:11151029:0629/005842.063296:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13170:11151029:0629/005842.063483:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T19:28:42.071Z","url":"https://www.semrush.com/"}
{"code":"BLOCKED_BY_WEBSITE","context":"BrowserController.initCluster","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"code":"BLOCKED_BY_WEBSITE","error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","isLargeWebsite":true,"level":"error","message":"Failed to execute task for https://www.semrush.com/:","retryable":false,"statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"code":"BLOCKED_BY_WEBSITE","context":"AIEnhancedScraperService.scrapeWithAI","level":"error","message":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","retryable":false,"stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","statusCode":403,"timestamp":"2025-06-28T19:29:12.110Z"}
{"error":"Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n","level":"error","message":"AI-enhanced scrape failed","stack":"ScraperError: Error in BrowserController.initCluster: Unable to launch browser, error message: Failed to launch the browser process! undefined\n[13403:11152100:0629/005912.104772:ERROR:chrome/browser/process_singleton_posix.cc:340] Failed to create /Users/<USER>/Desktop/totalads/totalads-scraper/puppeteer_temp/session_1751138809399_009fdq9om/SingletonLock: File exists (17)\n[13403:11152100:0629/005912.104948:ERROR:chrome/app/chrome_main_delegate.cc:531] Failed to create a ProcessSingleton for your profile directory. This means that running multiple instances would start multiple browser processes rather than opening a new window in the existing process. Aborting now to avoid profile corruption.\n\n\nTROUBLESHOOTING: https://pptr.dev/troubleshooting\n\n    at handleError (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/error-handler.ts:171:6)\n    at BrowserController.initCluster (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:54:4)\n    at process.processTicksAndRejections (node:internal/process/task_queues:105:5)\n    at async BrowserController.executeWithPageInternal (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:172:5)\n    at async CircuitBreaker.execute (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/retry-handler.ts:211:22)\n    at async BrowserController.executeWithPage (/Users/<USER>/Desktop/totalads/totalads-scraper/src/utils/scraper/browser-controller.ts:138:11)\n    at async AIEnhancedScraperService.executeBasicScraping (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:212:10)\n    at async AIEnhancedScraperService.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/core/ai-enhanced-scraper.service.ts:86:24)\n    at async AIEnhancedScraperController.scrapeWithAI (/Users/<USER>/Desktop/totalads/totalads-scraper/src/api/ai-enhanced-scraper.controller.ts:89:22)","timestamp":"2025-06-28T19:29:12.110Z","url":"https://www.semrush.com/"}
